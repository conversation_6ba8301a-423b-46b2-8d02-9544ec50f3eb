REACT_APP_GTM_ID=
REACT_APP_GOOGLE_CLIENT_ID=261382221700-keanutoauujrg1pbos6t8h365me4orlv.apps.googleusercontent.com
REACT_APP_HOTJAR_SITE_ID=
REACT_APP_ISSUE_COORDINATION_FEED_CHECK_INTERVAL_MS=30000
REACT_APP_ACTIVITIES_REFETCH_INTERVAL_MS=5000
REACT_APP_MICROSOFT_CLIENT_ID=6face787-fc09-40c5-951f-28109f703a1f
REACT_APP_NOTIFICATIONS_CHECK_INTERVAL_MS=60000
REACT_APP_PUSH_NOTIFICATIONS_PUBLIC_KEY=BNRxnVZeksZqmdhquhjc6O-4VWf-KsJ-CSrlkgF6uyBrz2Ag303y5ZD9ymhoR7pcwy0OtB-zEHJvDpokgNBeLKA=
REACT_APP_RAILS_ACTION_CABLE_URL=ws://localhost:3001
REACT_APP_RAILS_API_URL=http://localhost:3001
REACT_APP_SENTRY_DSN=
REACT_APP_SENTRY_ENVIRONMENT=
REACT_APP_STATUS_PAGE_EMBED_URL=
REACT_APP_SUPPORT_EMAIL=<EMAIL>
REACT_APP_VERSION=development
REACT_APP_REVISION=N/A
REACT_APP_USEBERRY=false
REACT_APP_HUBSPOT_KNOWLEDGE_BASE_URL=http://help.shape.construction/en-gb/

# Dev tools
REACT_APP_DISABLE_REACT_QUERY_DEV_TOOLS=false
REACT_APP_REDUX_DEVTOOLS_ENABLED=true

# Feature flags
REACT_APP_FEATURE_SHIFT_REPORT_CHAT_WEBSOCKETS=true

# Channels links
REACT_APP_CHANNELS_APP_STORE_URL=https://apps.apple.com/us/app/shape-channels-staging/id6475731494
REACT_APP_CHANNELS_PLAY_STORE_URL=https://play.google.com/store/apps/details?id=shapeconstruction.channels.staging
REACT_APP_CHANNELS_URL=shape-channels://

# Hubspot
REACT_APP_HUBSPOT_ONBOARDING_MEETING_LINK=https://meetings-eu1.hubspot.com/harry-rushworth/shape-setup-call?embed=true

# Cookiebot
REACT_APP_COOKIEBOT_DOMAIN_GROUP_ID=

# Stream Chat
REACT_APP_CHANNELS_STREAM_API_KEY=au4vue8te5ys

# Metabase
REACT_APP_METABASE_URL=https://metabase.shape-staging.construction

# Locale selector
REACT_APP_ENABLE_LOCALE_SELECTOR=true
