import React from 'react';
import type {
  PotentialChangeDetailsBasicSchema,
  PotentialChangeSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import { useModal } from '@shape-construction/hooks';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { getPotentialChangesInfiniteQueryOptions } from 'app/queries/control-center/commercial-tracker';
import { ChangeSignalsSummary } from './components/ChangeSignalsSummary';
import { PotentialChangeSignalsDrawer } from './components/PotentialChangeSignalsDrawer';
import { PotentialChangesEmptyState } from './components/PotentialChangesEmptyState';
import { PotentialChangesTable } from './components/PotentialChangesTable';

type CommercialTrackerProps = {
  project: ProjectSchema;
  isSelectionMode?: boolean;
  selectedPotentialChangeIds: Array<PotentialChangeSchema['id']> | undefined;
  setSelectedPotentialChangeIds: React.Dispatch<React.SetStateAction<Array<PotentialChangeSchema['id']> | undefined>>;
  titleRef: React.RefObject<HTMLInputElement>;
  onCreatePotentialChange: () => void;
  newRecordId?: PotentialChangeSchema['id'];
};

export const CommercialTracker: React.FC<CommercialTrackerProps> = ({
  project,
  isSelectionMode,
  selectedPotentialChangeIds,
  setSelectedPotentialChangeIds,
  titleRef,
  onCreatePotentialChange,
  newRecordId,
}) => {
  const [selectedPotentialChangeForSignals, setSelectedPotentialChangeForSignals] =
    React.useState<PotentialChangeDetailsBasicSchema>();

  const {
    data: potentialChangesData,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useSuspenseInfiniteQuery(getPotentialChangesInfiniteQueryOptions(project.id));

  const potentialChanges = potentialChangesData.entries;
  const totalChanges = potentialChangesData.meta.total;

  const {
    open: isPotentialChangeSignalsDrawerOpen,
    openModal: openPotentialChangeSignalsDrawer,
    closeModal: closePotentialChangeSignalsDrawer,
  } = useModal(false);

  const onViewChangeSignals = (potentialChange: PotentialChangeDetailsBasicSchema) => {
    setSelectedPotentialChangeForSignals(potentialChange);
    openPotentialChangeSignalsDrawer();
  };

  const onPotentialChangeSignalsClose = () => {
    setSelectedPotentialChangeForSignals(undefined);
    closePotentialChangeSignalsDrawer();
  };

  const onPreviousChange = (currentPotentialChangeId: PotentialChangeDetailsBasicSchema['id']) => {
    const currentIndex = potentialChanges.findIndex((change) => change.id === currentPotentialChangeId);
    const previousIndex = currentIndex === 0 ? potentialChanges.length - 1 : currentIndex - 1;
    setSelectedPotentialChangeForSignals(potentialChanges[previousIndex]);
  };

  const onNextChange = (currentPotentialChangeId: PotentialChangeDetailsBasicSchema['id']) => {
    const currentIndex = potentialChanges.findIndex((change) => change.id === currentPotentialChangeId);
    const nextIndex = currentIndex === potentialChanges.length - 1 ? 0 : currentIndex + 1;
    setSelectedPotentialChangeForSignals(potentialChanges[nextIndex]);
  };

  return (
    <div className="flex flex-col grow">
      <ChangeSignalsSummary
        isSelectionMode={isSelectionMode}
        totalChanges={totalChanges}
        onNewChangeClick={onCreatePotentialChange}
      />
      {potentialChanges.length > 0 ? (
        <>
          <PotentialChangesTable
            potentialChanges={potentialChanges}
            isSelectionMode={isSelectionMode}
            selectedPotentialChangeIds={selectedPotentialChangeIds}
            setSelectedPotentialChangeIds={setSelectedPotentialChangeIds}
            onViewChangeSignals={onViewChangeSignals}
            titleRef={titleRef}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            isFetchingNextPage={isFetchingNextPage}
            newRecordId={newRecordId}
            selectedPotentialChangeForSignals={selectedPotentialChangeForSignals}
          />
          {selectedPotentialChangeForSignals && (
            <PotentialChangeSignalsDrawer
              selectedPotentialChange={selectedPotentialChangeForSignals}
              setSelectedPotentialChange={setSelectedPotentialChangeForSignals}
              isOpen={isPotentialChangeSignalsDrawerOpen}
              onClose={onPotentialChangeSignalsClose}
              onPreviousChange={onPreviousChange}
              onNextChange={onNextChange}
            />
          )}
        </>
      ) : (
        <PotentialChangesEmptyState />
      )}
    </div>
  );
};
