import { useCallback, useState } from 'react';
import { useMessageGetter } from '@messageformat/react';
import { getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryKey } from '@shape-construction/api/src/hooks';
import type { ChangeSignalSchema, PotentialChangeDetailsBasicSchema } from '@shape-construction/api/src/types';
import { Button, ButtonGroup, type ButtonGroupOption, Drawer, IconButton } from '@shape-construction/arch-ui';
import {
  ArrowLeftIcon,
  ArrowsMaximizeIcon,
  ArrowsMinimizeIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  DocumentArrowDownIcon,
  InboxArrowDownIcon
} from '@shape-construction/arch-ui/src/Icons/solid';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useModal } from '@shape-construction/hooks';
import { useQueryClient, useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { useCurrentProject } from 'app/contexts/currentProject';
import {
  getChangeSignalsDowntimeInfiniteQueryOptions,
  getChangeSignalsInfiniteQueryOptions,
  useLinkChangeSignals,
  useUnlinkChangeSignals,
} from 'app/queries/control-center/change-signals';
import { useArchivePotentialChange, useGetPotentialChangeDetails } from 'app/queries/control-center/commercial-tracker';
import type { ChangeSignalListItem, PotentialChangeSignalsStep } from '../types';
import { canArchivePotentialChange } from '../utils/potentialChangeActions';
import { ArchiveChangeConfirmationModal } from './ArchiveChangeConfirmationModal';
import { ExportChangeConfirmationModal } from './ExportChangeConfirmationModal';
import { PotentialChangeSignalsLinking } from './PotentialChangeSignalsLinking';
import { PotentialChangeSignalsList } from './PotentialChangeSignalsList';
import { UnlinkConfirmationDialog } from './UnlinkConfirmationDialog';

type PotentialChangeSignalsDrawerProps = {
  isOpen: boolean;
  onClose: () => void;
  selectedPotentialChange: PotentialChangeDetailsBasicSchema;
  setSelectedPotentialChange: (potentialChange: PotentialChangeDetailsBasicSchema) => void;
  onPreviousChange: (currentPotentialChangeId: PotentialChangeDetailsBasicSchema['id']) => void;
  onNextChange: (currentPotentialChangeId: PotentialChangeDetailsBasicSchema['id']) => void;
};

export const PotentialChangeSignalsDrawer: React.FC<PotentialChangeSignalsDrawerProps> = ({
  isOpen,
  onClose,
  selectedPotentialChange,
  setSelectedPotentialChange,
  onPreviousChange,
  onNextChange
}) => {
  const queryClient = useQueryClient();
  const [currentStep, setCurrentStep] = useState<PotentialChangeSignalsStep>('VIEW');
  const [selectedChangeSignals, setSelectedChangeSignals] = useState<ChangeSignalListItem[]>([]);
  const [fullsceen, setFullscreen] = useState(false);
  const { value: isExportChangeEnabled } = useFeatureFlag('export-change');

  const {
    open: isUnlinkConfirmModalOpen,
    openModal: openUnlinkConfirmModal,
    closeModal: closeUnlinkConfirmModal,
  } = useModal(false);
  const {
    open: isArchiveChangeConfirmModalOpen,
    openModal: openArchiveChangeConfirmModal,
    closeModal: closeArchiveChangeConfirmModal,
  } = useModal(false);
  const {
    open: isExportChangeConfirmModalOpen,
    openModal: openExportChangeConfirmModal,
    closeModal: closeExportChangeConfirmModal,
  } = useModal(false);

  const [signalToUnlink, setSignalToUnlink] = useState<{
    signalId: ChangeSignalSchema['signalId'];
    signalType: ChangeSignalSchema['signalType'];
  } | null>(null);

  const project = useCurrentProject();
  const messages = useMessageGetter('controlCenter.commercialTracker.modals');
  const linkChangeSignalsMessages = useMessageGetter('controlCenter.changeSignals.linkStatus');

  const [selectedSignalType, setSelectedSignalType] = useState<ChangeSignalSchema['signalType']>('issue');

  const { data: potentialChange } = useGetPotentialChangeDetails(project.id, selectedPotentialChange.id);
  const issuesQuery = useSuspenseInfiniteQuery(getChangeSignalsInfiniteQueryOptions(project.id));
  const downtimeQuery = useSuspenseInfiniteQuery(getChangeSignalsDowntimeInfiniteQueryOptions(project.id));
  const currentQuery = selectedSignalType === 'issue' ? issuesQuery : downtimeQuery;
  const currentChangeSignals = currentQuery.data.signals ?? [];

  const issuesCount = issuesQuery.data.total;
  const downtimeCount = downtimeQuery.data.total;

  const { mutateAsync: linkChangeSignals, isPending: isLinkingChangeSignals } = useLinkChangeSignals();
  const { mutateAsync: unlinkChangeSignals, isPending: isUnlinkingChangeSignals } = useUnlinkChangeSignals();
  const { mutateAsync: archivePotentialChange, isPending: isArchivePending } = useArchivePotentialChange();

  const resetState = () => {
    setCurrentStep('VIEW');
    setSelectedChangeSignals([]);
    setSelectedSignalType('issue');
    setFullscreen(false);
  };

  const onDrawerClose = () => {
    resetState();
    onClose();
  };

  const onLinkChangeSignals = async () => {
    try {
      const potentialChangeResponse = await linkChangeSignals({
        projectId: project.id,
        potentialChangeId: selectedPotentialChange.id,
        data: {
          change_signals: selectedChangeSignals.map((signal) => ({
            change_signal_type: signal.signalType,
            change_signal_id: signal.signalId,
          })),
        },
      });

      await queryClient.invalidateQueries({
        queryKey: getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryKey(
          project.id,
          selectedPotentialChange.id
        ),
      });

      showSuccessToast({
        message: linkChangeSignalsMessages('linking.success'),
      });
      setSelectedPotentialChange(potentialChangeResponse);
      resetState();
    } catch (error) {
      showErrorToast({
        message: linkChangeSignalsMessages('linking.failed'),
      });
    }
  };

  const onUnlinkChangeSignalClick = async (
    signalId: ChangeSignalSchema['signalId'],
    signalType: ChangeSignalSchema['signalType']
  ) => {
    setSignalToUnlink({ signalId, signalType });
    openUnlinkConfirmModal();
  };

  const handleUnlinkConfirm = async () => {
    if (!signalToUnlink) return;
    try {
      await unlinkChangeSignals({
        projectId: project.id,
        potentialChangeId: selectedPotentialChange.id,
        data: {
          change_signals: [
            {
              change_signal_id: signalToUnlink.signalId,
              change_signal_type: signalToUnlink.signalType,
            },
          ],
        },
      });
      await queryClient.invalidateQueries({
        queryKey: getApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryKey(
          project.id,
          selectedPotentialChange.id
        ),
      });
      showSuccessToast({
        message: linkChangeSignalsMessages('unlinking.success'),
      });
    } catch {
      showErrorToast({
        message: linkChangeSignalsMessages('unlinking.failed'),
      });
    } finally {
      closeUnlinkConfirmModal();
      setSignalToUnlink(null);
    }
  };

  const onArchive = () => {
    closeArchiveChangeConfirmModal();
    archivePotentialChange({
      projectId: project.id,
      potentialChangeId: selectedPotentialChange.id,
    });
    onDrawerClose();
  };

  const canArchive = canArchivePotentialChange(selectedPotentialChange);

  const handleLoadMore = useCallback(() => {
    if (currentQuery.hasNextPage && !currentQuery.isFetchingNextPage) {
      currentQuery.fetchNextPage();
    }
  }, [currentQuery]);

  const toggleFullscreen = () => {
    setFullscreen(!fullsceen);
  };

  const drawerRightContent = (
    <div className="flex items-center gap-2">
      {canArchive && (
        <Button
          onClick={openArchiveChangeConfirmModal}
          color="secondary"
          size="xs"
          variant="outlined"
          leadingIcon={InboxArrowDownIcon}
          tabIndex={-1}
          disabled={isArchivePending}
          aria-label={messages('potentialChangeLinkSignalsDrawer.archiveCTA')}
        >
          {messages('potentialChangeLinkSignalsDrawer.archiveCTA')}
        </Button>
      )}
      {isExportChangeEnabled && (
        <Button
          onClick={openExportChangeConfirmModal}
          color="primary"
          size="xs"
          variant="contained"
          leadingIcon={DocumentArrowDownIcon}
          tabIndex={-1}
          disabled={isArchivePending}
          aria-label={messages('exportChange.exportCTA')}
        >
          {messages('exportChange.exportCTA')}
        </Button>
      )}
      <IconButton
        shape="square"
        color="secondary"
        size="xs"
        variant="outlined"
        icon={fullsceen ? ArrowsMinimizeIcon : ArrowsMaximizeIcon}
        onClick={toggleFullscreen}
        tabIndex={-1}
      />
    </div>
  );

  const canDrawerClose =
    !isUnlinkConfirmModalOpen && !isArchiveChangeConfirmModalOpen && !isExportChangeConfirmModalOpen;

  const renderContent = () => {
    switch (currentStep) {
      case 'LINK':
        return (
          potentialChange && (
            <PotentialChangeSignalsLinking
              potentialChange={potentialChange}
              selectedChangeSignals={selectedChangeSignals}
              setSelectedChangeSignals={setSelectedChangeSignals}
              onLinkChangeSignals={onLinkChangeSignals}
              changeSignals={currentChangeSignals}
              issuesCount={issuesCount}
              downtimeCount={downtimeCount}
              selectedSignalType={selectedSignalType}
              setSelectedSignalType={setSelectedSignalType}
              onLoadMore={handleLoadMore}
              hasNextPage={currentQuery.hasNextPage}
              isFetchingNextPage={currentQuery.isFetchingNextPage}
            />
          )
        );
      case 'VIEW':
        return (
          potentialChange && (
            <PotentialChangeSignalsList
              potentialChange={potentialChange}
              onLinkChangeSignalsClick={() => setCurrentStep('LINK')}
              onUnlinkChangeSignalClick={onUnlinkChangeSignalClick}
              isUnlinkingChangeSignals={isUnlinkingChangeSignals}
            />
          )
        );
      default:
        return null;
    }
  };

  const textAndIconsOptions: ButtonGroupOption[] = [
    {
      id: '1',
      icon: ChevronUpIcon,
      color: "secondary",
      onClick: () => onPreviousChange(selectedPotentialChange.id),
      tooltip: 'Previous change',
    },
    {
      id: '2',
      icon: ChevronDownIcon,
      color: "secondary",
      onClick: () => onNextChange(selectedPotentialChange.id),
      tooltip: 'Next change',
    },
  ];

  return (
    <>
      <Drawer.Root open={isOpen} onClose={canDrawerClose ? onClose : () => {}} maxWidth={fullsceen ? 'none' : '2/3'}>
        <Drawer.Header className="p-4 flex flex-row gap-2" onClose={onDrawerClose} rightContent={drawerRightContent}>
          <Drawer.Title className="flex gap-3 items-center h-8">
            {currentStep === 'LINK' ? (
              <IconButton
                onClick={() => setCurrentStep('VIEW')}
                icon={ArrowLeftIcon}
                color={'secondary'}
                size="xs"
                variant={'text'}
              />
            ) : (
              <ButtonGroup options={textAndIconsOptions} size="sm" variant="outlined" tabIndex={-1} />
            )}
          </Drawer.Title>
        </Drawer.Header>
        <Drawer.Content className="flex flex-col gap-4 pb-4">{renderContent()}</Drawer.Content>
      </Drawer.Root>
      <UnlinkConfirmationDialog
        isOpen={isUnlinkConfirmModalOpen}
        onClose={() => {
          closeUnlinkConfirmModal();
          setSignalToUnlink(null);
        }}
        onUnlinkConfirm={handleUnlinkConfirm}
        isUnlinking={isUnlinkingChangeSignals}
      />
      <ArchiveChangeConfirmationModal
        isOpen={isArchiveChangeConfirmModalOpen}
        closeModal={closeArchiveChangeConfirmModal}
        onArchive={onArchive}
      />
      <ExportChangeConfirmationModal
        changeId={selectedPotentialChange.id}
        open={isExportChangeConfirmModalOpen}
        onClose={closeExportChangeConfirmModal}
      />
    </>
  );
};
